"""
Pokemon Move Animation Extractor
Extracts and processes Pokemon battle move animations from ROM data
"""

import os
import json
from pathlib import Path
from typing import List, Dict, Optional, Tuple, Any
from PIL import Image
import struct

from rom_reader import Pokemon<PERSON>MReader
from graphics_decoder import DSGraphicsDecoder


class MoveAnimationExtractor:
    """
    Extracts Pokemon move animations from ROM files
    """
    
    def __init__(self, rom_reader: PokemonROMReader):
        """
        Initialize the move extractor
        
        Args:
            rom_reader: Initialized ROM reader instance
        """
        self.rom_reader = rom_reader
        self.graphics_decoder = DSGraphicsDecoder()
        self.move_list = []
        self.move_data = {}
        self.output_dir = "extracted_moves"
        
        # Move name mappings (partial list for common moves)
        self.move_names = {
            1: "Pound", 2: "Karate Chop", 3: "Double Slap", 4: "Comet Punch",
            5: "Mega Punch", 6: "Pay Day", 7: "Fire Punch", 8: "Ice Punch",
            9: "Thunder Punch", 10: "Scratch", 11: "Vise Grip", 12: "Guillotine",
            13: "Razor Wind", 14: "Swords Dance", 15: "Cut", 16: "Gust",
            17: "Wing Attack", 18: "Whirlwind", 19: "Fly", 20: "Bind",
            21: "Slam", 22: "Vine Whip", 23: "Stomp", 24: "Double Kick",
            25: "Mega Kick", 26: "Jump Kick", 27: "Rolling Kick", 28: "Sand Attack",
            29: "Headbutt", 30: "Horn Attack", 31: "Fury Attack", 32: "Horn Drill",
            33: "Tackle", 34: "Body Slam", 35: "Wrap", 36: "Take Down",
            37: "Thrash", 38: "Double-Edge", 39: "Tail Whip", 40: "Poison Sting",
            41: "Twineedle", 42: "Pin Missile", 43: "Leer", 44: "Bite",
            45: "Growl", 46: "Roar", 47: "Sing", 48: "Supersonic",
            49: "Sonic Boom", 50: "Disable", 51: "Acid", 52: "Ember",
            53: "Flamethrower", 54: "Mist", 55: "Water Gun", 56: "Hydro Pump",
            57: "Surf", 58: "Ice Beam", 59: "Blizzard", 60: "Psybeam",
            61: "Bubble Beam", 62: "Aurora Beam", 63: "Hyper Beam", 64: "Peck",
            65: "Drill Peck", 66: "Submission", 67: "Low Kick", 68: "Counter",
            69: "Seismic Toss", 70: "Strength", 71: "Absorb", 72: "Mega Drain",
            73: "Leech Seed", 74: "Growth", 75: "Razor Leaf", 76: "Solar Beam",
            77: "Poison Powder", 78: "Stun Spore", 79: "Sleep Powder", 80: "Petal Dance",
            81: "String Shot", 82: "Dragon Rage", 83: "Fire Spin", 84: "Thunder Shock",
            85: "Thunderbolt", 86: "Thunder Wave", 87: "Thunder", 88: "Rock Throw",
            89: "Earthquake", 90: "Fissure", 91: "Dig", 92: "Toxic",
            93: "Confusion", 94: "Psychic", 95: "Hypnosis", 96: "Meditate",
            97: "Agility", 98: "Quick Attack", 99: "Rage", 100: "Teleport"
        }
    
    def initialize_output_directory(self):
        """
        Create the output directory structure
        """
        Path(self.output_dir).mkdir(exist_ok=True)
        Path(f"{self.output_dir}/frames").mkdir(exist_ok=True)
        Path(f"{self.output_dir}/backgrounds").mkdir(exist_ok=True)
        Path(f"{self.output_dir}/effects").mkdir(exist_ok=True)
        Path(f"{self.output_dir}/sprites").mkdir(exist_ok=True)
    
    def find_move_data_files(self) -> List[str]:
        """
        Find files containing move data and animations
        
        Returns:
            List of relevant filenames
        """
        move_files = []
        
        # Common move-related file patterns for different Pokemon games
        patterns = {
            'HeartGold': ['a/0/1/1', 'a/0/8/', 'a/1/2/', 'a/1/9/'],
            'SoulSilver': ['a/0/1/1', 'a/0/8/', 'a/1/2/', 'a/1/9/'],
            'Black': ['a/0/1/1', 'a/0/8/', 'a/1/2/', 'a/1/9/'],
            'White': ['a/0/1/1', 'a/0/8/', 'a/1/2/', 'a/1/9/'],
            'Black2': ['a/0/1/1', 'a/0/8/', 'a/1/2/', 'a/1/9/'],
            'White2': ['a/0/1/1', 'a/0/8/', 'a/1/2/', 'a/1/9/']
        }
        
        game_version = self.rom_reader.game_info.get('version', 'Unknown')
        search_patterns = patterns.get(game_version, patterns['HeartGold'])
        
        for filename in self.rom_reader.rom.filenames:
            if filename:
                for pattern in search_patterns:
                    if pattern in filename:
                        move_files.append(filename)
        
        return move_files
    
    def load_move_list(self) -> bool:
        """
        Load the list of moves from the ROM
        
        Returns:
            bool: True if successful
        """
        try:
            # Look for move data file (usually waza_tbl.narc or similar)
            move_data_file = self.rom_reader.get_file_by_name('a/0/1/1')
            if not move_data_file:
                # Try alternative paths
                for alt_path in ['waza_tbl.narc', 'move_data.narc']:
                    move_data_file = self.rom_reader.get_file_by_name(alt_path)
                    if move_data_file:
                        break
            
            if move_data_file:
                # Extract NARC archive
                move_files = self.rom_reader.extract_narc_archive(move_data_file)
                
                # Parse move data
                for i, file_data in move_files.items():
                    if len(file_data) >= 12:  # Minimum move data size
                        move_info = self.parse_move_data(file_data, i)
                        if move_info:
                            self.move_list.append(move_info)
                
                print(f"Loaded {len(self.move_list)} moves")
                return True
            
            # Fallback: create dummy move list
            for i in range(1, 101):  # First 100 moves
                move_name = self.move_names.get(i, f"Move {i}")
                self.move_list.append({
                    'id': i,
                    'name': move_name,
                    'type': 0,
                    'power': 0,
                    'accuracy': 0
                })
            
            return True
            
        except Exception as e:
            print(f"Error loading move list: {e}")
            return False
    
    def parse_move_data(self, data: bytes, move_id: int) -> Optional[Dict[str, Any]]:
        """
        Parse individual move data
        
        Args:
            data: Raw move data
            move_id: Move ID number
            
        Returns:
            Dictionary containing move information
        """
        try:
            if len(data) < 12:
                return None
            
            # Basic move data structure (varies by game)
            effect, power, type_id, accuracy, pp, effect_chance = struct.unpack('<HBBBBH', data[:8])
            
            move_name = self.move_names.get(move_id, f"Move {move_id}")
            
            return {
                'id': move_id,
                'name': move_name,
                'effect': effect,
                'power': power,
                'type': type_id,
                'accuracy': accuracy,
                'pp': pp,
                'effect_chance': effect_chance
            }
            
        except Exception as e:
            print(f"Error parsing move data for move {move_id}: {e}")
            return None
    
    def find_move_animations(self, move_id: int) -> List[Dict[str, Any]]:
        """
        Find animation files for a specific move

        Args:
            move_id: ID of the move to find animations for

        Returns:
            List of animation file information
        """
        animations = []

        # Look for animation files in common locations for different games
        game_version = self.rom_reader.game_info.get('version', 'Unknown')

        if game_version in ['HeartGold', 'SoulSilver']:
            # HGSS specific paths
            animation_paths = [
                f'a/0/8/{move_id}',  # Move effects
                f'a/1/2/{move_id}',  # Move animations
                f'a/1/9/{move_id}',  # Additional effects
                'data/kowaza.narc',  # Small move effects
                'data/demo_climax.narc',  # Special effects
            ]
        else:
            # BW/B2W2 specific paths
            animation_paths = [
                f'a/0/8/{move_id}',  # Move effects
                f'a/1/2/{move_id}',  # Move animations
                f'a/1/9/{move_id}',  # Additional effects
                f'a/2/1/{move_id}',  # Battle effects
            ]

        # Also look for general graphics files that might contain move data
        graphics_files = self.rom_reader.get_graphics_files()
        for filename, file_id in graphics_files.items():
            if 'waza' in filename.lower() or 'battle' in filename.lower() or 'effect' in filename.lower():
                file_data = self.rom_reader.get_file_by_index(file_id)
                if file_data:
                    animations.append({
                        'path': filename,
                        'data': file_data,
                        'type': 'graphics',
                        'file_id': file_id
                    })

        # Look for specific move files
        for path in animation_paths:
            file_data = self.rom_reader.get_file_by_name(path)
            if file_data:
                animations.append({
                    'path': path,
                    'data': file_data,
                    'type': 'animation'
                })

        return animations
    
    def extract_move_graphics(self, move_id: int) -> Dict[str, List[Image.Image]]:
        """
        Extract all graphics components for a move
        
        Args:
            move_id: ID of the move to extract
            
        Returns:
            Dictionary containing lists of images by type
        """
        extracted = {
            'frames': [],
            'backgrounds': [],
            'effects': [],
            'sprites': []
        }
        
        try:
            # Find animation files for this move
            animations = self.find_move_animations(move_id)
            
            for anim in animations:
                # Try to decompress if needed
                data = anim['data']
                if data.startswith(b'\x10'):  # LZ10 compressed
                    decompressed = self.rom_reader.decompress_lz10(data)
                    if decompressed:
                        data = decompressed
                
                # Check if it's a NARC archive
                if data.startswith(b'NARC'):
                    archive_files = self.rom_reader.extract_narc_archive(data)
                    
                    for file_idx, file_data in archive_files.items():
                        images = self.process_graphics_file(file_data)
                        if images:
                            # Categorize images based on file index or content
                            if file_idx == 0:
                                extracted['backgrounds'].extend(images)
                            elif file_idx < 5:
                                extracted['effects'].extend(images)
                            else:
                                extracted['sprites'].extend(images)
                else:
                    # Process as single graphics file
                    images = self.process_graphics_file(data)
                    if images:
                        extracted['frames'].extend(images)
            
        except Exception as e:
            print(f"Error extracting graphics for move {move_id}: {e}")
        
        return extracted
    
    def process_graphics_file(self, data: bytes) -> List[Image.Image]:
        """
        Process a graphics file and extract images

        Args:
            data: Raw graphics file data

        Returns:
            List of extracted images
        """
        images = []

        try:
            format_type = self.graphics_decoder.detect_format(data)

            if format_type == 'NCGR':
                char_data = self.graphics_decoder.read_ncgr(data)
                if char_data:
                    # Try to find a matching palette
                    palette_data = self.find_matching_palette()
                    if palette_data:
                        img = self.graphics_decoder.create_image_from_tiles(char_data, palette_data)
                        if img:
                            images.append(img)
                    else:
                        # Create with default palette
                        default_palette = self.create_default_palette()
                        img = self.graphics_decoder.create_image_from_tiles(char_data, default_palette)
                        if img:
                            images.append(img)

            elif format_type == 'NCLR':
                palette_data = self.graphics_decoder.read_nclr(data)
                if palette_data:
                    # Create color swatch
                    images.append(self.create_palette_image(palette_data))

            elif format_type == 'NSCR':
                screen_data = self.graphics_decoder.read_nscr(data)
                if screen_data:
                    # Screen data - try to find matching graphics and palette
                    char_data = self.find_matching_graphics()
                    palette_data = self.find_matching_palette()
                    if char_data and palette_data:
                        img = self.graphics_decoder.create_image_from_tiles(char_data, palette_data, screen_data)
                        if img:
                            images.append(img)

            # Handle NARC archives
            elif data.startswith(b'NARC'):
                archive_files = self.rom_reader.extract_narc_archive(data)
                for file_idx, file_data in archive_files.items():
                    sub_images = self.process_graphics_file(file_data)
                    images.extend(sub_images)

            # Handle compressed data
            elif data.startswith(b'\x10'):  # LZ10 compressed
                decompressed = self.rom_reader.decompress_lz10(data)
                if decompressed:
                    sub_images = self.process_graphics_file(decompressed)
                    images.extend(sub_images)

            # If no specific format detected, try to extract raw image data
            else:
                raw_image = self.try_extract_raw_image(data)
                if raw_image:
                    images.append(raw_image)

        except Exception as e:
            print(f"Error processing graphics file: {e}")

        return images

    def find_matching_palette(self) -> Optional[Dict[str, Any]]:
        """
        Find a matching palette file from the ROM
        """
        try:
            # Look for common palette files
            graphics_files = self.rom_reader.get_graphics_files()
            for filename, file_id in graphics_files.items():
                if filename.lower().endswith('.nclr'):
                    file_data = self.rom_reader.get_file_by_index(file_id)
                    if file_data:
                        palette_data = self.graphics_decoder.read_nclr(file_data)
                        if palette_data:
                            return palette_data
        except Exception as e:
            print(f"Error finding palette: {e}")

        return None

    def find_matching_graphics(self) -> Optional[Dict[str, Any]]:
        """
        Find matching graphics data from the ROM
        """
        try:
            graphics_files = self.rom_reader.get_graphics_files()
            for filename, file_id in graphics_files.items():
                if filename.lower().endswith('.ncgr'):
                    file_data = self.rom_reader.get_file_by_index(file_id)
                    if file_data:
                        char_data = self.graphics_decoder.read_ncgr(file_data)
                        if char_data:
                            return char_data
        except Exception as e:
            print(f"Error finding graphics: {e}")

        return None

    def create_default_palette(self) -> Dict[str, Any]:
        """
        Create a default grayscale palette
        """
        palette = []
        for i in range(256):
            gray = i
            palette.extend([gray, gray, gray, 255 if i > 0 else 0])

        return {
            'type': 'NCLR',
            'palette': palette,
            'colors': 256
        }

    def try_extract_raw_image(self, data: bytes) -> Optional[Image.Image]:
        """
        Try to extract image from raw data
        """
        try:
            # Try different common sizes
            sizes = [(64, 64), (128, 128), (256, 256), (32, 32), (16, 16)]

            for width, height in sizes:
                if len(data) >= width * height:
                    # Try as 8-bit grayscale
                    img = Image.new('L', (width, height))
                    pixels = list(data[:width * height])
                    img.putdata(pixels)
                    return img.convert('RGBA')

        except Exception as e:
            print(f"Error extracting raw image: {e}")

        return None
    
    def create_placeholder_image(self, width: int, height: int, label: str) -> Image.Image:
        """
        Create a placeholder image with label
        """
        img = Image.new('RGBA', (width, height), (128, 128, 128, 255))
        return img
    
    def create_palette_image(self, palette_data: Dict[str, Any]) -> Image.Image:
        """
        Create an image showing the palette colors
        """
        palette = palette_data['palette']
        colors_per_row = 16
        color_size = 16
        
        rows = (len(palette) // 4 + colors_per_row - 1) // colors_per_row
        img_width = colors_per_row * color_size
        img_height = rows * color_size
        
        img = Image.new('RGBA', (img_width, img_height), (255, 255, 255, 255))
        pixels = img.load()
        
        for i in range(0, len(palette), 4):
            color_idx = i // 4
            row = color_idx // colors_per_row
            col = color_idx % colors_per_row
            
            r, g, b, a = palette[i:i+4]
            
            for y in range(row * color_size, (row + 1) * color_size):
                for x in range(col * color_size, (col + 1) * color_size):
                    if x < img_width and y < img_height:
                        pixels[x, y] = (r, g, b, a)
        
        return img
    
    def save_move_data(self, move_id: int, extracted_data: Dict[str, List[Image.Image]]):
        """
        Save extracted move data to files
        
        Args:
            move_id: Move ID
            extracted_data: Dictionary of extracted images
        """
        move_name = self.move_names.get(move_id, f"Move_{move_id}")
        move_dir = Path(f"{self.output_dir}/{move_name}_{move_id}")
        move_dir.mkdir(exist_ok=True)
        
        # Save each category of images
        for category, images in extracted_data.items():
            category_dir = move_dir / category
            category_dir.mkdir(exist_ok=True)
            
            for i, img in enumerate(images):
                filename = f"{category}_{i:03d}.png"
                img.save(category_dir / filename)
        
        # Save move metadata
        move_info = next((m for m in self.move_list if m['id'] == move_id), None)
        if move_info:
            with open(move_dir / "move_info.json", 'w') as f:
                json.dump(move_info, f, indent=2)
    
    def get_move_list(self) -> List[Dict[str, Any]]:
        """
        Get the list of available moves
        
        Returns:
            List of move dictionaries
        """
        return self.move_list.copy()
