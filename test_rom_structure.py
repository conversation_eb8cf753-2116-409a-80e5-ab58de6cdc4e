"""
Test script to understand ROM structure
"""

import ndspy.rom

def explore_rom_structure(rom_path):
    """
    Explore the structure of a ROM file
    """
    print(f"Loading ROM: {rom_path}")
    rom = ndspy.rom.NintendoDSRom.fromFile(rom_path)
    
    print(f"ROM name: {rom.name}")
    print(f"ROM ID: {rom.idCode}")
    print(f"Total files: {len(rom.files)}")
    
    print("\nExploring filenames structure:")
    print(f"Filenames type: {type(rom.filenames)}")
    print(f"Filenames dir: {dir(rom.filenames)}")
    
    # Try to access files and folders
    try:
        print(f"Files attribute: {hasattr(rom.filenames, 'files')}")
        print(f"Folders attribute: {hasattr(rom.filenames, 'folders')}")
        
        if hasattr(rom.filenames, 'files'):
            files = rom.filenames.files
            print(f"Files type: {type(files)}")
            print(f"Files length: {len(files) if hasattr(files, '__len__') else 'No length'}")
            if len(files) > 0:
                print(f"First few files: {files[:5]}")
        
        if hasattr(rom.filenames, 'folders'):
            folders = rom.filenames.folders
            print(f"Folders type: {type(folders)}")
            print(f"Folders length: {len(folders) if hasattr(folders, '__len__') else 'No length'}")
            if len(folders) > 0:
                print(f"First few folders: {folders[:5]}")
                
        # Try to get all filenames
        if hasattr(rom.filenames, 'allFilenames'):
            all_filenames = rom.filenames.allFilenames()
            print(f"All filenames: {len(all_filenames)} files")
            for i, filename in enumerate(all_filenames[:10]):
                print(f"  {i}: {filename}")
                
    except Exception as e:
        print(f"Error exploring structure: {e}")
    
    # Try to find specific files
    print("\nLooking for specific files:")
    for i in range(min(20, len(rom.files))):
        if rom.files[i]:
            print(f"File {i}: {len(rom.files[i])} bytes")
            # Check if it's a known format
            data = rom.files[i]
            if data.startswith(b'NARC'):
                print(f"  -> NARC archive")
            elif data.startswith(b'RGCN'):
                print(f"  -> NCGR graphics")
            elif data.startswith(b'RLCN'):
                print(f"  -> NCLR palette")

if __name__ == "__main__":
    rom_files = [
        "4780 - Pokemon HeartGold (U)(Xenophobia).nds",
        "6149 - Pokemon - Black Version 2 (U) (frieNDS).nds"
    ]
    
    for rom_file in rom_files:
        try:
            explore_rom_structure(rom_file)
            break
        except Exception as e:
            print(f"Error with {rom_file}: {e}")
