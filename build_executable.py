"""
Build script for creating a standalone executable
"""

import subprocess
import sys
import os
from pathlib import Path

def install_pyinstaller():
    """Install PyInstaller if not already installed"""
    try:
        import PyInstaller
        print("PyInstaller is already installed")
    except ImportError:
        print("Installing PyInstaller...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])

def build_executable():
    """Build the executable using PyInstaller"""
    
    # Install PyInstaller if needed
    install_pyinstaller()
    
    # Create the executable
    cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed",
        "--name", "PokemonMoveExtractor",
        "--add-data", "requirements.txt;.",
        "pokemon_move_extractor_gui.py"
    ]

    # Add icon if it exists
    if os.path.exists("icon.ico"):
        cmd.insert(-2, "--icon")
        cmd.insert(-2, "icon.ico")
    
    print(f"Building executable with command: {' '.join(cmd)}")
    
    try:
        subprocess.check_call(cmd)
        print("\nExecutable built successfully!")
        print("You can find it in the 'dist' folder.")
        
        # Check if executable was created
        exe_path = Path("dist/PokemonMoveExtractor.exe")
        if exe_path.exists():
            print(f"Executable size: {exe_path.stat().st_size / (1024*1024):.1f} MB")
        
    except subprocess.CalledProcessError as e:
        print(f"Error building executable: {e}")
        return False
    
    return True

def create_spec_file():
    """Create a custom .spec file for more control"""
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['pokemon_move_extractor_gui.py'],
    pathex=[],
    binaries=[],
    datas=[('requirements.txt', '.')],
    hiddenimports=['ndspy', 'ndspy.rom', 'ndspy.narc', 'ndspy.lz10', 'ndspy.fnt'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='PokemonMoveExtractor',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open("pokemon_move_extractor.spec", "w") as f:
        f.write(spec_content)
    
    print("Created custom .spec file")

if __name__ == "__main__":
    print("Pokemon Move Extractor - Build Script")
    print("=" * 40)
    
    # Create spec file for better control
    create_spec_file()
    
    # Build the executable
    if build_executable():
        print("\nBuild completed successfully!")
        print("\nTo run the executable:")
        print("1. Navigate to the 'dist' folder")
        print("2. Run 'PokemonMoveExtractor.exe'")
        print("3. Place your Pokemon ROM files in the same directory")
    else:
        print("\nBuild failed. Please check the error messages above.")
