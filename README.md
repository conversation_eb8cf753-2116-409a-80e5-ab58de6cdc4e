# Pokemon Move Animation Extractor

A comprehensive tool for extracting Pokemon battle move animations from Nintendo DS ROM files. This application can parse Pokemon HeartGold/SoulSilver and Black/White series ROMs to extract move animations and decompose them into their constituent graphical components.

## Features

- **ROM Support**: Compatible with Pokemon HeartGold, SoulSilver, Black, White, Black 2, and White 2
- **Move Animation Extraction**: Extract complete move animations from ROM files
- **Component Decomposition**: Break down animations into:
  - Individual animation frames (PNG)
  - Background elements/layers (PNG)
  - Sprite components and effects (PNG)
  - Particle effects and overlays (PNG)
- **User-Friendly GUI**: Intuitive interface for browsing and extracting moves
- **Organized Output**: Structured directory system for extracted components
- **Preview System**: View extracted images directly in the application

## Installation

### Option 1: Use Pre-built Executable (Recommended)
1. Download the latest release from the releases page
2. Extract the ZIP file to a folder of your choice
3. Run `PokemonMoveExtractor.exe`

### Option 2: Run from Source
1. Ensure you have Python 3.7+ installed
2. Clone or download this repository
3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
4. Run the application:
   ```bash
   python pokemon_move_extractor_gui.py
   ```

## Usage

### Getting Started
1. **Launch the Application**: Run the executable or Python script
2. **Load a ROM**: 
   - Click "Browse" to select your Pokemon ROM file (.nds)
   - Or place ROM files in the same directory for auto-detection
   - Click "Load ROM" to parse the file

### Extracting Move Animations
1. **Select a Move**: 
   - Browse the move list on the left side
   - Use the search box to filter moves by name or ID
   - Click on a move to select it

2. **Extract Animation**:
   - Click "Extract Selected Move" to begin extraction
   - The process will analyze the ROM and extract all related graphics
   - Progress will be shown in the log area

3. **View Results**:
   - Extracted files are saved in the `extracted_moves` folder
   - Use the preview panel to view extracted images
   - Navigate through images using Previous/Next buttons

### Output Structure
```
extracted_moves/
├── Move_Name_001/
│   ├── frames/           # Animation frames
│   ├── backgrounds/      # Background elements
│   ├── effects/          # Effect sprites
│   ├── sprites/          # Other sprite components
│   └── move_info.json    # Move metadata
└── ...
```

## Supported ROM Files

The application supports the following Pokemon games:
- Pokemon HeartGold (US/EU/JP)
- Pokemon SoulSilver (US/EU/JP)
- Pokemon Black (US/EU/JP)
- Pokemon White (US/EU/JP)
- Pokemon Black 2 (US/EU/JP)
- Pokemon White 2 (US/EU/JP)

**Note**: ROM files must be in .nds format (Nintendo DS ROM files).

## Technical Details

### Graphics Formats Supported
- **NCGR**: Nintendo Character Graphics Resource (tile graphics)
- **NCLR**: Nintendo Color Resource (palettes)
- **NSCR**: Nintendo Screen Resource (tilemaps)
- **NCER**: Nintendo Cell Resource (sprite cells)
- **NANR**: Nintendo Animation Resource (animations)

### Compression Support
- **LZ10**: Standard Nintendo DS compression format
- **NARC**: Nintendo Archive format for file containers

## Troubleshooting

### Common Issues

**"Failed to load ROM"**
- Ensure the ROM file is a valid .nds file
- Check that the ROM is not corrupted
- Try a different ROM dump

**"No moves found"**
- The ROM structure may be different from expected
- Some ROM hacks or modified files may not be supported
- Check the log for specific error messages

**"Extraction failed"**
- Some moves may not have animation data in the expected format
- Graphics may be stored in a different location in the ROM
- Check available disk space for output files

**Application won't start**
- Ensure all dependencies are installed (if running from source)
- Check that Python 3.7+ is installed
- Try running from command line to see error messages

### Performance Tips
- Close other applications to free up memory during extraction
- Extract moves one at a time for better stability
- Ensure sufficient disk space (extracted files can be large)

## Building from Source

To create your own executable:

1. Install PyInstaller:
   ```bash
   pip install pyinstaller
   ```

2. Run the build script:
   ```bash
   python build_executable.py
   ```

3. The executable will be created in the `dist` folder

## Legal Notice

This tool is for educational and research purposes only. Users must own legal copies of the Pokemon games they wish to extract data from. The developers are not responsible for any misuse of this software.

Pokemon is a trademark of Nintendo, Game Freak, and The Pokemon Company. This project is not affiliated with or endorsed by these companies.

## Contributing

Contributions are welcome! Please feel free to submit issues, feature requests, or pull requests.

## License

This project is released under the MIT License. See LICENSE file for details.

## Credits

- Built using the excellent [ndspy](https://github.com/RoadrunnerWMC/ndspy) library for Nintendo DS file format support
- Graphics processing powered by [Pillow](https://python-pillow.org/)
- GUI created with Python's built-in tkinter library

## Version History

### v1.0.0
- Initial release
- Support for HeartGold/SoulSilver and Black/White series
- Basic move animation extraction
- GUI interface with preview system
- Organized output structure
