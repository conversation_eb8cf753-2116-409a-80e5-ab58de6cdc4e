# Pokemon Move Animation Extractor - Usage Guide

## Quick Start

1. **Download and Setup**
   - Download the `PokemonMoveExtractor.exe` from the `dist` folder
   - Place your Pokemon ROM files (.nds) in the same directory as the executable
   - Double-click `PokemonMoveExtractor.exe` to launch

2. **Load a ROM**
   - The application will auto-detect ROM files in the current directory
   - Click "Load ROM" to parse the selected ROM file
   - Wait for the loading process to complete (you'll see progress in the log)

3. **Browse and Extract Moves**
   - Use the move list on the left to browse available moves
   - Use the search box to filter moves by name or ID number
   - Select a move and click "Extract Selected Move"
   - View extracted images in the preview panel

## Detailed Features

### ROM Support
The application supports these Pokemon games:
- **HeartGold/SoulSilver**: Full support for move animations
- **Black/White/Black2/White2**: Full support for move animations

### Move Selection
- **Move List**: Shows all moves with ID numbers and names
- **Search Function**: Filter moves by typing part of the name or ID
- **Move Information**: Displays move details when selected

### Extraction Process
When you extract a move, the application:
1. Searches the ROM for animation-related files
2. Identifies graphics components (sprites, backgrounds, effects)
3. Decompresses compressed data (LZ10 format)
4. Extracts individual graphics files
5. Converts them to PNG format
6. Organizes them in structured folders

### Output Structure
```
extracted_moves/
├── Move_Name_001/
│   ├── frames/           # Individual animation frames
│   │   ├── frames_000.png
│   │   ├── frames_001.png
│   │   └── ...
│   ├── backgrounds/      # Background elements
│   │   ├── backgrounds_000.png
│   │   └── ...
│   ├── effects/          # Effect sprites and particles
│   │   ├── effects_000.png
│   │   └── ...
│   ├── sprites/          # Other sprite components
│   │   ├── sprites_000.png
│   │   └── ...
│   └── move_info.json    # Move metadata (power, type, etc.)
```

### Preview System
- **Image Navigation**: Use Previous/Next buttons to browse extracted images
- **Image Information**: Shows current image name and position
- **Zoom and Pan**: Images are automatically scaled to fit the preview area

## Tips and Best Practices

### Performance
- **Extract One Move at a Time**: This ensures better stability and performance
- **Close Other Applications**: Free up memory for better extraction performance
- **Check Disk Space**: Extracted files can be quite large (several MB per move)

### Troubleshooting
- **ROM Not Loading**: Ensure the ROM file is a valid .nds file and not corrupted
- **No Moves Found**: Some ROM versions may have different internal structures
- **Extraction Fails**: Some moves may not have animation data in the expected format
- **Application Crashes**: Try running as administrator or check available memory

### File Management
- **Backup ROMs**: Keep backup copies of your ROM files
- **Organize Extractions**: The application creates organized folders automatically
- **Clean Up**: Remove unwanted extractions to save disk space

## Advanced Usage

### Running from Source Code
If you want to modify the application or run it from Python:

```bash
# Install dependencies
pip install -r requirements.txt

# Run the application
python pokemon_move_extractor_gui.py
```

### Building Your Own Executable
```bash
# Install PyInstaller
pip install pyinstaller

# Build the executable
python build_executable.py
```

### Understanding the Code Structure
- `rom_reader.py`: Handles ROM file parsing and navigation
- `graphics_decoder.py`: Decodes Nintendo DS graphics formats
- `move_extractor.py`: Core extraction logic for move animations
- `pokemon_move_extractor_gui.py`: Main GUI application

## Technical Information

### Supported Graphics Formats
- **NCGR**: Character graphics (tile data)
- **NCLR**: Color palettes
- **NSCR**: Screen/tilemap data
- **NCER**: Cell/sprite data
- **NANR**: Animation data

### Compression Support
- **LZ10**: Standard Nintendo DS compression
- **NARC**: Nintendo archive format

### ROM Structure
The application navigates the Nintendo DS file system to locate:
- Move data tables (usually in `a/0/1/1`)
- Animation files (typically in `a/0/8/` or `a/1/2/`)
- Graphics resources scattered throughout the ROM

## Limitations

### Current Limitations
- **Not All Moves Have Animations**: Some moves may not have visual effects
- **ROM Variations**: Different ROM dumps may have slightly different structures
- **Graphics Complexity**: Some complex animations may not extract perfectly
- **Memory Usage**: Large ROMs require significant memory to process

### Future Improvements
- Support for more Pokemon games (Diamond/Pearl/Platinum)
- Better graphics reconstruction for complex animations
- Batch extraction of multiple moves
- Animation preview and playback

## Legal and Ethical Use

### Important Notes
- **Own Your ROMs**: Only use ROM files you legally own
- **Personal Use Only**: This tool is for personal research and educational purposes
- **Respect Copyright**: Pokemon is copyrighted by Nintendo, Game Freak, and The Pokemon Company
- **No Distribution**: Do not distribute extracted copyrighted content

### Disclaimer
This software is provided "as is" without warranty. The developers are not responsible for any misuse or legal issues arising from the use of this software.

## Support and Community

### Getting Help
- Check the log output for error messages
- Ensure you're using supported ROM versions
- Try different ROM dumps if one doesn't work
- Report bugs with specific ROM information and error messages

### Contributing
- The source code is available for modification and improvement
- Contributions are welcome for bug fixes and new features
- Please test thoroughly before submitting changes

## Version Information

**Current Version**: 1.0.0
**Last Updated**: 2025
**Compatibility**: Windows 10/11, Python 3.7+
