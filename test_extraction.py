"""
Test script to verify move extraction is working
"""

import os
from rom_reader import PokemonROMRead<PERSON>
from move_extractor import MoveAnimationExtractor

def test_move_extraction():
    """
    Test the move extraction functionality
    """
    print("Pokemon Move Extraction Test")
    print("=" * 40)
    
    # Find ROM files
    rom_files = [
        "4780 - Pokemon HeartGold (U)(Xenophobia).nds",
        "6149 - Pokemon - Black Version 2 (U) (frieNDS).nds"
    ]
    
    rom_file = None
    for rf in rom_files:
        if os.path.exists(rf):
            rom_file = rf
            break
    
    if not rom_file:
        print("No ROM files found!")
        return False
    
    print(f"Using ROM: {rom_file}")
    
    # Initialize ROM reader
    print("Loading ROM...")
    rom_reader = PokemonROMReader(rom_file)
    if not rom_reader.load_rom():
        print("Failed to load ROM!")
        return False
    
    print("ROM loaded successfully!")
    rom_info = rom_reader.get_rom_info()
    print(f"Game: {rom_info['game_info']['title']} ({rom_info['game_info']['version']})")
    print(f"Total files: {rom_info['total_files']}")
    print(f"Graphics files: {rom_info['graphics_files']}")
    
    # Initialize move extractor
    print("\nInitializing move extractor...")
    move_extractor = MoveAnimationExtractor(rom_reader)
    move_extractor.initialize_output_directory()
    
    if not move_extractor.load_move_list():
        print("Failed to load move list!")
        return False
    
    moves = move_extractor.get_move_list()
    print(f"Loaded {len(moves)} moves")
    
    # Test extraction with a few common moves
    test_moves = [1, 25, 52, 85, 100]  # Pound, Thunder Punch, Ember, Thunderbolt, Teleport
    
    for move_id in test_moves:
        move_info = next((m for m in moves if m['id'] == move_id), None)
        if not move_info:
            print(f"Move {move_id} not found in list")
            continue
        
        print(f"\nTesting extraction for Move {move_id}: {move_info['name']}")
        
        # Find animation files
        animations = move_extractor.find_move_animations(move_id)
        print(f"Found {len(animations)} animation files")
        
        if animations:
            for i, anim in enumerate(animations[:3]):  # Test first 3 files
                print(f"  Animation {i+1}: {anim['path']} ({len(anim['data'])} bytes)")
        
        # Extract graphics
        print("Extracting graphics...")
        extracted_data = move_extractor.extract_move_graphics(move_id)
        
        total_images = sum(len(images) for images in extracted_data.values())
        print(f"Extracted {total_images} images:")
        
        for category, images in extracted_data.items():
            if images:
                print(f"  {category}: {len(images)} images")
        
        if total_images > 0:
            # Save the extracted data
            move_extractor.save_move_data(move_id, extracted_data)
            print(f"Saved to extracted_moves/{move_info['name']}_{move_id}/")
            
            # Check if files were actually created
            move_dir = f"extracted_moves/{move_info['name']}_{move_id}"
            if os.path.exists(move_dir):
                files_created = []
                for root, dirs, files in os.walk(move_dir):
                    for file in files:
                        if file.endswith('.png'):
                            files_created.append(os.path.join(root, file))
                
                print(f"Created {len(files_created)} PNG files")
                if files_created:
                    print("Sample files:")
                    for file_path in files_created[:5]:  # Show first 5 files
                        file_size = os.path.getsize(file_path)
                        print(f"  {file_path} ({file_size} bytes)")
            
            break  # Test only one move for now
        else:
            print("No images extracted")
    
    print("\nTest completed!")
    return True

def test_graphics_files():
    """
    Test processing of graphics files directly
    """
    print("\nTesting graphics file processing...")
    
    rom_files = [
        "4780 - Pokemon HeartGold (U)(Xenophobia).nds",
        "6149 - Pokemon - Black Version 2 (U) (frieNDS).nds"
    ]
    
    rom_file = None
    for rf in rom_files:
        if os.path.exists(rf):
            rom_file = rf
            break
    
    if not rom_file:
        return False
    
    rom_reader = PokemonROMReader(rom_file)
    if not rom_reader.load_rom():
        return False
    
    move_extractor = MoveAnimationExtractor(rom_reader)
    
    # Get graphics files
    graphics_files = rom_reader.get_graphics_files()
    print(f"Found {len(graphics_files)} graphics files")
    
    # Test processing a few graphics files
    test_count = 0
    for filename, file_id in graphics_files.items():
        if test_count >= 5:  # Test only first 5 files
            break
        
        print(f"\nTesting file: {filename}")
        file_data = rom_reader.get_file_by_index(file_id)
        
        if file_data:
            print(f"File size: {len(file_data)} bytes")
            format_type = move_extractor.graphics_decoder.detect_format(file_data)
            print(f"Detected format: {format_type}")
            
            images = move_extractor.process_graphics_file(file_data)
            print(f"Extracted {len(images)} images")
            
            if images:
                # Save test images
                test_dir = "test_graphics"
                os.makedirs(test_dir, exist_ok=True)
                
                for i, img in enumerate(images):
                    safe_filename = filename.replace('/', '_').replace('\\', '_')
                    img_path = f"{test_dir}/{safe_filename}_{i}.png"
                    img.save(img_path)
                    print(f"Saved: {img_path} ({img.size})")
                
                test_count += 1
    
    return True

if __name__ == "__main__":
    # Test move extraction
    success = test_move_extraction()
    
    if success:
        # Test graphics file processing
        test_graphics_files()
    
    print("\nAll tests completed!")
