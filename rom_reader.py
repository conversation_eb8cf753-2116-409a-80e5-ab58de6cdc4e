"""
Nintendo DS ROM Reader Module
Handles parsing and reading of Nintendo DS ROM files (.nds format)
"""

import ndspy.rom
import ndspy.narc
import ndspy.lz10
import os
import struct
from typing import List, Dict, Optional, Tuple, Any
from pathlib import Path


class PokemonROMReader:
    """
    A class to read and parse Pokemon Nintendo DS ROM files
    """
    
    def __init__(self, rom_path: str):
        """
        Initialize the ROM reader with a ROM file path
        
        Args:
            rom_path: Path to the .nds ROM file
        """
        self.rom_path = rom_path
        self.rom = None
        self.game_info = {}
        self.move_data = {}
        self.graphics_files = {}
        
    def load_rom(self) -> bool:
        """
        Load the ROM file and extract basic information
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.rom = ndspy.rom.NintendoDSRom.fromFile(self.rom_path)
            self._detect_game_version()
            self._index_files()
            return True
        except Exception as e:
            print(f"Error loading ROM: {e}")
            return False
    
    def _detect_game_version(self):
        """
        Detect which Pokemon game this ROM is
        """
        game_code = self.rom.idCode.decode('ascii', errors='ignore')
        game_title = self.rom.name.decode('ascii', errors='ignore').strip()
        
        self.game_info = {
            'code': game_code,
            'title': game_title,
            'version': 'Unknown'
        }
        
        # Detect specific Pokemon games
        if 'IPKE' in game_code or 'IPKJ' in game_code:
            self.game_info['version'] = 'HeartGold'
        elif 'IPGE' in game_code or 'IPGJ' in game_code:
            self.game_info['version'] = 'SoulSilver'
        elif 'IRBO' in game_code or 'IRBE' in game_code:
            self.game_info['version'] = 'Black'
        elif 'IRAO' in game_code or 'IRAE' in game_code:
            self.game_info['version'] = 'White'
        elif 'IRE' in game_code:
            self.game_info['version'] = 'Black2'
        elif 'IRD' in game_code:
            self.game_info['version'] = 'White2'
        
        print(f"Detected game: {self.game_info['title']} ({self.game_info['version']})")
    
    def _index_files(self):
        """
        Index all files in the ROM for quick access
        """
        self.file_index = {}

        # Handle the ROM file system structure
        try:
            if hasattr(self.rom, 'filenames') and hasattr(self.rom, 'files'):
                # ROM filenames is a Folder object, need to iterate through it properly
                self._index_folder(self.rom.filenames, "")

        except Exception as e:
            print(f"Error indexing files: {e}")
            # Create a minimal index
            self.file_index = {}

    def _index_folder(self, folder, path_prefix: str):
        """
        Recursively index files in a folder

        Args:
            folder: ndspy.fnt.Folder object
            path_prefix: Current path prefix
        """
        try:
            # Index files in this folder - files are stored as a list of filenames with firstID
            if hasattr(folder, 'files') and hasattr(folder, 'firstID'):
                for i, filename in enumerate(folder.files):
                    file_id = folder.firstID + i
                    full_path = f"{path_prefix}/{filename}" if path_prefix else filename
                    self.file_index[full_path.lower()] = file_id

                    # Look for graphics-related files
                    if any(ext in filename.lower() for ext in ['.ncgr', '.nclr', '.nscr', '.ncer', '.nanr']):
                        self.graphics_files[full_path] = file_id

            # Recursively index subfolders
            if hasattr(folder, 'folders'):
                for subfolder_name, subfolder_obj in folder.folders:
                    subfolder_path = f"{path_prefix}/{subfolder_name}" if path_prefix else subfolder_name
                    self._index_folder(subfolder_obj, subfolder_path)

        except Exception as e:
            print(f"Error indexing folder {path_prefix}: {e}")
    
    def get_file_by_name(self, filename: str) -> Optional[bytes]:
        """
        Get file data by filename
        
        Args:
            filename: Name of the file to retrieve
            
        Returns:
            bytes: File data if found, None otherwise
        """
        filename_lower = filename.lower()
        if filename_lower in self.file_index:
            file_index = self.file_index[filename_lower]
            return self.rom.files[file_index]
        return None
    
    def get_file_by_index(self, index: int) -> Optional[bytes]:
        """
        Get file data by index
        
        Args:
            index: Index of the file to retrieve
            
        Returns:
            bytes: File data if found, None otherwise
        """
        if 0 <= index < len(self.rom.files):
            return self.rom.files[index]
        return None
    
    def extract_narc_archive(self, narc_data: bytes) -> Dict[int, bytes]:
        """
        Extract files from a NARC archive
        
        Args:
            narc_data: Raw NARC archive data
            
        Returns:
            Dict mapping file indices to file data
        """
        try:
            narc = ndspy.narc.NARC(narc_data)
            return {i: file_data for i, file_data in enumerate(narc.files)}
        except Exception as e:
            print(f"Error extracting NARC: {e}")
            return {}
    
    def decompress_lz10(self, data: bytes) -> Optional[bytes]:
        """
        Decompress LZ10 compressed data
        
        Args:
            data: Compressed data
            
        Returns:
            Decompressed data if successful, None otherwise
        """
        try:
            return ndspy.lz10.decompress(data)
        except Exception as e:
            print(f"Error decompressing LZ10 data: {e}")
            return None
    
    def find_move_animation_files(self) -> List[str]:
        """
        Find files that likely contain move animation data

        Returns:
            List of filenames that may contain move animations
        """
        animation_files = []

        # Common patterns for move animation files in Pokemon games
        patterns = [
            'waza',  # Move-related files
            'battle',  # Battle-related files
            'effect',  # Effect files
            'demo',  # Demo/animation files
            'particle',  # Particle effects
        ]

        try:
            # Search through indexed files
            for filename in self.file_index.keys():
                if any(pattern in filename for pattern in patterns):
                    animation_files.append(filename)
        except Exception as e:
            print(f"Error finding animation files: {e}")

        return animation_files
    
    def get_graphics_files(self) -> Dict[str, int]:
        """
        Get all graphics-related files in the ROM
        
        Returns:
            Dictionary mapping filenames to file indices
        """
        return self.graphics_files.copy()
    
    def get_rom_info(self) -> Dict[str, Any]:
        """
        Get information about the loaded ROM
        
        Returns:
            Dictionary containing ROM information
        """
        if not self.rom:
            return {}
        
        return {
            'game_info': self.game_info,
            'total_files': len(self.rom.files),
            'graphics_files': len(self.graphics_files),
            'rom_size': len(self.rom.save()),
            'arm9_size': len(self.rom.arm9),
            'arm7_size': len(self.rom.arm7),
        }


def test_rom_reader():
    """
    Test function for the ROM reader
    """
    # Test with available ROM files
    rom_files = [
        "4780 - Pokemon HeartGold (U)(Xenophobia).nds",
        "6149 - Pokemon - Black Version 2 (U) (frieNDS).nds"
    ]
    
    for rom_file in rom_files:
        if os.path.exists(rom_file):
            print(f"\nTesting with {rom_file}")
            reader = PokemonROMReader(rom_file)
            
            if reader.load_rom():
                info = reader.get_rom_info()
                print(f"ROM Info: {info}")
                
                animation_files = reader.find_move_animation_files()
                print(f"Found {len(animation_files)} potential animation files")
                
                graphics_files = reader.get_graphics_files()
                print(f"Found {len(graphics_files)} graphics files")
                
                # Show first few animation files
                for i, filename in enumerate(animation_files[:5]):
                    print(f"  {filename}")
                
                break
            else:
                print(f"Failed to load {rom_file}")


if __name__ == "__main__":
    test_rom_reader()
