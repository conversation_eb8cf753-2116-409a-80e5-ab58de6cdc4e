"""
Nintendo DS Graphics Format Decoder
Handles decoding of NCGR, NCLR, NSCR, NCER, NANR and other DS graphics formats
"""

import struct
import numpy as np
from PIL import Image
from typing import List, Tuple, Optional, Dict, Any
import io


class DSGraphicsDecoder:
    """
    Decoder for Nintendo DS graphics formats
    """
    
    def __init__(self):
        self.palettes = {}
        self.character_data = {}
        self.screen_data = {}
        self.cell_data = {}
        self.animation_data = {}
    
    def read_ncgr(self, data: bytes) -> Optional[Dict[str, Any]]:
        """
        Read NCGR (Nintendo Character Graphics Resource) file
        
        Args:
            data: Raw NCGR file data
            
        Returns:
            Dictionary containing character graphics data
        """
        try:
            if not data.startswith(b'RGCN'):
                return None
            
            # Parse NCGR header
            header = struct.unpack('<4sHHI', data[:12])
            magic, bom, version, file_size = header
            
            # Find CHAR section
            char_offset = data.find(b'RAHC')
            if char_offset == -1:
                return None
            
            char_header = struct.unpack('<4sIHHHHII', data[char_offset:char_offset+24])
            char_magic, char_size, height, width, format_info, flags, data_offset, unknown = char_header
            
            # Extract character data
            char_data_start = char_offset + data_offset
            char_data_end = char_data_start + (char_size - data_offset)
            char_data = data[char_data_start:char_data_end]
            
            return {
                'type': 'NCGR',
                'width': width,
                'height': height,
                'format': format_info & 0x7,
                'char_data': char_data,
                'tile_width': 8,
                'tile_height': 8
            }
            
        except Exception as e:
            print(f"Error reading NCGR: {e}")
            return None
    
    def read_nclr(self, data: bytes) -> Optional[Dict[str, Any]]:
        """
        Read NCLR (Nintendo Color Resource) file
        
        Args:
            data: Raw NCLR file data
            
        Returns:
            Dictionary containing palette data
        """
        try:
            if not data.startswith(b'RLCN'):
                return None
            
            # Parse NCLR header
            header = struct.unpack('<4sHHI', data[:12])
            magic, bom, version, file_size = header
            
            # Find TTLP section (palette)
            pltt_offset = data.find(b'PTLP')
            if pltt_offset == -1:
                return None
            
            pltt_header = struct.unpack('<4sIHHI', data[pltt_offset:pltt_offset+16])
            pltt_magic, pltt_size, depth, unknown, data_size = pltt_header
            
            # Extract palette data
            palette_start = pltt_offset + 16
            palette_end = palette_start + data_size
            palette_data = data[palette_start:palette_end]
            
            # Convert to RGB palette
            palette = []
            for i in range(0, len(palette_data), 2):
                if i + 1 < len(palette_data):
                    color = struct.unpack('<H', palette_data[i:i+2])[0]
                    r = (color & 0x1F) << 3
                    g = ((color >> 5) & 0x1F) << 3
                    b = ((color >> 10) & 0x1F) << 3
                    a = 255 if color != 0 else 0  # Transparent if color is 0
                    palette.extend([r, g, b, a])
            
            return {
                'type': 'NCLR',
                'depth': depth,
                'palette': palette,
                'colors': len(palette) // 4
            }
            
        except Exception as e:
            print(f"Error reading NCLR: {e}")
            return None
    
    def read_nscr(self, data: bytes) -> Optional[Dict[str, Any]]:
        """
        Read NSCR (Nintendo Screen Resource) file
        
        Args:
            data: Raw NSCR file data
            
        Returns:
            Dictionary containing screen/tilemap data
        """
        try:
            if not data.startswith(b'RCSN'):
                return None
            
            # Parse NSCR header
            header = struct.unpack('<4sHHI', data[:12])
            magic, bom, version, file_size = header
            
            # Find NRCS section
            scrn_offset = data.find(b'NRCS')
            if scrn_offset == -1:
                return None
            
            scrn_header = struct.unpack('<4sIHHHHII', data[scrn_offset:scrn_offset+24])
            scrn_magic, scrn_size, width, height, color_depth, bg_type, data_size, unknown = scrn_header
            
            # Extract screen data
            screen_start = scrn_offset + 24
            screen_end = screen_start + data_size
            screen_data = data[screen_start:screen_end]
            
            return {
                'type': 'NSCR',
                'width': width,
                'height': height,
                'color_depth': color_depth,
                'bg_type': bg_type,
                'screen_data': screen_data
            }
            
        except Exception as e:
            print(f"Error reading NSCR: {e}")
            return None
    
    def read_ncer(self, data: bytes) -> Optional[Dict[str, Any]]:
        """
        Read NCER (Nintendo Cell Resource) file
        
        Args:
            data: Raw NCER file data
            
        Returns:
            Dictionary containing cell data
        """
        try:
            if not data.startswith(b'RECN'):
                return None
            
            # Parse NCER header
            header = struct.unpack('<4sHHI', data[:12])
            magic, bom, version, file_size = header
            
            # Find KBEC section
            cebk_offset = data.find(b'KBEC')
            if cebk_offset == -1:
                return None
            
            # This is a simplified parser - full NCER parsing is quite complex
            return {
                'type': 'NCER',
                'data': data[cebk_offset:]
            }
            
        except Exception as e:
            print(f"Error reading NCER: {e}")
            return None
    
    def read_nanr(self, data: bytes) -> Optional[Dict[str, Any]]:
        """
        Read NANR (Nintendo Animation Resource) file
        
        Args:
            data: Raw NANR file data
            
        Returns:
            Dictionary containing animation data
        """
        try:
            if not data.startswith(b'RNAN'):
                return None
            
            # Parse NANR header
            header = struct.unpack('<4sHHI', data[:12])
            magic, bom, version, file_size = header
            
            # Find KNBA section
            abnk_offset = data.find(b'KNBA')
            if abnk_offset == -1:
                return None
            
            # This is a simplified parser - full NANR parsing is quite complex
            return {
                'type': 'NANR',
                'data': data[abnk_offset:]
            }
            
        except Exception as e:
            print(f"Error reading NANR: {e}")
            return None
    
    def create_image_from_tiles(self, char_data: Dict[str, Any], palette_data: Dict[str, Any],
                               screen_data: Optional[Dict[str, Any]] = None) -> Optional[Image.Image]:
        """
        Create a PIL Image from character and palette data

        Args:
            char_data: Character graphics data from NCGR
            palette_data: Palette data from NCLR
            screen_data: Optional screen data from NSCR

        Returns:
            PIL Image if successful, None otherwise
        """
        try:
            if not char_data or not palette_data:
                return None

            char_bytes = char_data['char_data']
            palette = palette_data['palette']
            format_type = char_data.get('format', 3)  # Default to 4bpp

            # Determine bits per pixel
            if format_type == 3:  # 4bpp
                bpp = 4
            elif format_type == 4:  # 8bpp
                bpp = 8
            else:
                bpp = 4  # Default

            # Calculate image dimensions - use tile-based approach
            tiles_per_row = 16  # Default tile arrangement
            tile_size = 8  # 8x8 pixel tiles

            if screen_data:
                img_width = screen_data['width'] * tile_size
                img_height = screen_data['height'] * tile_size
            else:
                # Calculate based on character data size
                total_tiles = len(char_bytes) // (64 // (bpp // 4))  # 64 bytes per 4bpp tile, 32 for 8bpp
                tiles_per_row = min(tiles_per_row, int(np.sqrt(total_tiles)) + 1)
                tile_rows = (total_tiles + tiles_per_row - 1) // tiles_per_row

                img_width = tiles_per_row * tile_size
                img_height = tile_rows * tile_size

            if img_width <= 0 or img_height <= 0:
                img_width, img_height = 64, 64  # Fallback size

            # Create image
            img = Image.new('RGBA', (img_width, img_height), (0, 0, 0, 0))
            pixels = img.load()

            # Process tiles
            bytes_per_tile = 64 if bpp == 4 else 32
            tile_index = 0

            for byte_offset in range(0, len(char_bytes), bytes_per_tile):
                if tile_index >= (img_width // tile_size) * (img_height // tile_size):
                    break

                tile_x = (tile_index % (img_width // tile_size)) * tile_size
                tile_y = (tile_index // (img_width // tile_size)) * tile_size

                # Process this tile
                tile_data = char_bytes[byte_offset:byte_offset + bytes_per_tile]
                self._render_tile(pixels, tile_data, tile_x, tile_y, palette, bpp, img_width, img_height)

                tile_index += 1

            return img

        except Exception as e:
            print(f"Error creating image: {e}")
            return None

    def _render_tile(self, pixels, tile_data: bytes, start_x: int, start_y: int,
                    palette: List[int], bpp: int, img_width: int, img_height: int):
        """
        Render a single 8x8 tile to the image
        """
        try:
            pixel_index = 0

            for byte_idx in range(len(tile_data)):
                byte_val = tile_data[byte_idx]

                if bpp == 4:
                    # 4 bits per pixel, 2 pixels per byte
                    for shift in [0, 4]:
                        if pixel_index >= 64:  # 8x8 = 64 pixels per tile
                            break

                        palette_idx = (byte_val >> shift) & 0xF

                        # Calculate pixel position within tile
                        tile_pixel_x = pixel_index % 8
                        tile_pixel_y = pixel_index // 8

                        # Calculate absolute pixel position
                        abs_x = start_x + tile_pixel_x
                        abs_y = start_y + tile_pixel_y

                        if abs_x < img_width and abs_y < img_height:
                            if palette_idx * 4 + 3 < len(palette):
                                r = palette[palette_idx * 4]
                                g = palette[palette_idx * 4 + 1]
                                b = palette[palette_idx * 4 + 2]
                                a = palette[palette_idx * 4 + 3] if palette_idx > 0 else 0  # Index 0 is transparent
                                pixels[abs_x, abs_y] = (r, g, b, a)

                        pixel_index += 1

                elif bpp == 8:
                    # 8 bits per pixel, 1 pixel per byte
                    if pixel_index >= 64:
                        break

                    palette_idx = byte_val

                    # Calculate pixel position within tile
                    tile_pixel_x = pixel_index % 8
                    tile_pixel_y = pixel_index // 8

                    # Calculate absolute pixel position
                    abs_x = start_x + tile_pixel_x
                    abs_y = start_y + tile_pixel_y

                    if abs_x < img_width and abs_y < img_height:
                        if palette_idx * 4 + 3 < len(palette):
                            r = palette[palette_idx * 4]
                            g = palette[palette_idx * 4 + 1]
                            b = palette[palette_idx * 4 + 2]
                            a = palette[palette_idx * 4 + 3] if palette_idx > 0 else 0
                            pixels[abs_x, abs_y] = (r, g, b, a)

                    pixel_index += 1

        except Exception as e:
            print(f"Error rendering tile: {e}")
    
    def detect_format(self, data: bytes) -> Optional[str]:
        """
        Detect the format of graphics data
        
        Args:
            data: Raw file data
            
        Returns:
            Format string if detected, None otherwise
        """
        if data.startswith(b'RGCN'):
            return 'NCGR'
        elif data.startswith(b'RLCN'):
            return 'NCLR'
        elif data.startswith(b'RCSN'):
            return 'NSCR'
        elif data.startswith(b'RECN'):
            return 'NCER'
        elif data.startswith(b'RNAN'):
            return 'NANR'
        else:
            return None
