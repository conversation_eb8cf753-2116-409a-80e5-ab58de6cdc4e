"""
Pokemon Move Animation Extractor GUI
Main application with user interface for extracting Pokemon move animations
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
from pathlib import Path
from PIL import Image, ImageTk
import json

from rom_reader import PokemonROMReader
from move_extractor import MoveAnimationExtractor


class PokemonMoveExtractorGUI:
    """
    Main GUI application for Pokemon Move Animation Extractor
    """
    
    def __init__(self, root):
        """
        Initialize the GUI application
        
        Args:
            root: Tkinter root window
        """
        self.root = root
        self.root.title("Pokemon Move Animation Extractor")
        self.root.geometry("1200x800")
        
        # Application state
        self.rom_reader = None
        self.move_extractor = None
        self.current_move_id = None
        self.extracted_images = {}
        
        # Create GUI elements
        self.create_widgets()
        
        # Check for ROM files in current directory
        self.auto_detect_roms()
    
    def create_widgets(self):
        """
        Create and layout GUI widgets
        """
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # ROM Selection Frame
        rom_frame = ttk.LabelFrame(main_frame, text="ROM Selection", padding="5")
        rom_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        rom_frame.columnconfigure(1, weight=1)
        
        ttk.Label(rom_frame, text="ROM File:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        
        self.rom_path_var = tk.StringVar()
        self.rom_entry = ttk.Entry(rom_frame, textvariable=self.rom_path_var, state="readonly")
        self.rom_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 5))
        
        self.browse_button = ttk.Button(rom_frame, text="Browse", command=self.browse_rom)
        self.browse_button.grid(row=0, column=2, padx=(0, 5))
        
        self.load_button = ttk.Button(rom_frame, text="Load ROM", command=self.load_rom)
        self.load_button.grid(row=0, column=3)
        
        # ROM Info
        self.rom_info_label = ttk.Label(rom_frame, text="No ROM loaded")
        self.rom_info_label.grid(row=1, column=0, columnspan=4, sticky=tk.W, pady=(5, 0))
        
        # Move Selection Frame
        move_frame = ttk.LabelFrame(main_frame, text="Move Selection", padding="5")
        move_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        move_frame.rowconfigure(1, weight=1)
        
        # Move search
        search_frame = ttk.Frame(move_frame)
        search_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        search_frame.columnconfigure(1, weight=1)
        
        ttk.Label(search_frame, text="Search:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.filter_moves)
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var)
        search_entry.grid(row=0, column=1, sticky=(tk.W, tk.E))
        
        # Move list
        list_frame = ttk.Frame(move_frame)
        list_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        list_frame.rowconfigure(0, weight=1)
        list_frame.columnconfigure(0, weight=1)
        
        self.move_listbox = tk.Listbox(list_frame, height=15)
        self.move_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.move_listbox.bind('<<ListboxSelect>>', self.on_move_select)
        
        move_scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.move_listbox.yview)
        move_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.move_listbox.configure(yscrollcommand=move_scrollbar.set)
        
        # Extract button
        self.extract_button = ttk.Button(move_frame, text="Extract Selected Move", 
                                       command=self.extract_current_move, state="disabled")
        self.extract_button.grid(row=2, column=0, pady=(5, 0))
        
        # Preview Frame
        preview_frame = ttk.LabelFrame(main_frame, text="Preview", padding="5")
        preview_frame.grid(row=1, column=1, rowspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(10, 0))
        preview_frame.rowconfigure(1, weight=1)
        preview_frame.columnconfigure(0, weight=1)
        
        # Preview controls
        control_frame = ttk.Frame(preview_frame)
        control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        self.prev_button = ttk.Button(control_frame, text="Previous", command=self.prev_image, state="disabled")
        self.prev_button.grid(row=0, column=0, padx=(0, 5))
        
        self.image_info_label = ttk.Label(control_frame, text="No image")
        self.image_info_label.grid(row=0, column=1, padx=5)
        
        self.next_button = ttk.Button(control_frame, text="Next", command=self.next_image, state="disabled")
        self.next_button.grid(row=0, column=2, padx=(5, 0))
        
        # Image display
        self.image_canvas = tk.Canvas(preview_frame, bg="white", width=400, height=400)
        self.image_canvas.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Current image tracking
        self.current_images = []
        self.current_image_index = 0
        
        # Log Frame
        log_frame = ttk.LabelFrame(main_frame, text="Log", padding="5")
        log_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.rowconfigure(0, weight=1)
        log_frame.columnconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=10, width=50)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
    
    def log_message(self, message: str):
        """
        Add a message to the log
        
        Args:
            message: Message to log
        """
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def auto_detect_roms(self):
        """
        Auto-detect ROM files in the current directory
        """
        current_dir = Path(".")
        rom_files = list(current_dir.glob("*.nds"))
        
        if rom_files:
            self.rom_path_var.set(str(rom_files[0]))
            self.log_message(f"Auto-detected ROM: {rom_files[0]}")
    
    def browse_rom(self):
        """
        Browse for a ROM file
        """
        filename = filedialog.askopenfilename(
            title="Select Pokemon ROM file",
            filetypes=[("Nintendo DS ROM", "*.nds"), ("All files", "*.*")]
        )
        
        if filename:
            self.rom_path_var.set(filename)
    
    def load_rom(self):
        """
        Load the selected ROM file
        """
        rom_path = self.rom_path_var.get()
        if not rom_path or not os.path.exists(rom_path):
            messagebox.showerror("Error", "Please select a valid ROM file")
            return
        
        self.status_var.set("Loading ROM...")
        self.log_message(f"Loading ROM: {rom_path}")
        
        # Load ROM in a separate thread to prevent GUI freezing
        threading.Thread(target=self._load_rom_thread, args=(rom_path,), daemon=True).start()
    
    def _load_rom_thread(self, rom_path: str):
        """
        Load ROM in a separate thread
        
        Args:
            rom_path: Path to the ROM file
        """
        try:
            # Create ROM reader
            self.rom_reader = PokemonROMReader(rom_path)
            
            if not self.rom_reader.load_rom():
                self.root.after(0, lambda: messagebox.showerror("Error", "Failed to load ROM"))
                return
            
            # Create move extractor
            self.move_extractor = MoveAnimationExtractor(self.rom_reader)
            self.move_extractor.initialize_output_directory()
            
            # Load move list
            if not self.move_extractor.load_move_list():
                self.root.after(0, lambda: messagebox.showwarning("Warning", "Failed to load move list"))
            
            # Update GUI on main thread
            self.root.after(0, self._update_gui_after_load)
            
        except Exception as e:
            error_msg = f"Error loading ROM: {str(e)}"
            self.root.after(0, lambda: messagebox.showerror("Error", error_msg))
            self.root.after(0, lambda: self.log_message(error_msg))
    
    def _update_gui_after_load(self):
        """
        Update GUI after ROM is loaded
        """
        # Update ROM info
        rom_info = self.rom_reader.get_rom_info()
        game_info = rom_info.get('game_info', {})
        info_text = f"Game: {game_info.get('title', 'Unknown')} ({game_info.get('version', 'Unknown')})"
        self.rom_info_label.config(text=info_text)
        
        # Populate move list
        self.populate_move_list()
        
        # Enable controls
        self.extract_button.config(state="normal")
        
        self.status_var.set("ROM loaded successfully")
        self.log_message("ROM loaded successfully")
        self.log_message(f"Found {len(self.move_extractor.get_move_list())} moves")
    
    def populate_move_list(self):
        """
        Populate the move list widget
        """
        self.move_listbox.delete(0, tk.END)
        
        if self.move_extractor:
            moves = self.move_extractor.get_move_list()
            for move in moves:
                display_text = f"{move['id']:03d} - {move['name']}"
                self.move_listbox.insert(tk.END, display_text)
    
    def filter_moves(self, *args):
        """
        Filter the move list based on search text
        """
        if not self.move_extractor:
            return
        
        search_text = self.search_var.get().lower()
        self.move_listbox.delete(0, tk.END)
        
        moves = self.move_extractor.get_move_list()
        for move in moves:
            display_text = f"{move['id']:03d} - {move['name']}"
            if search_text in move['name'].lower() or search_text in str(move['id']):
                self.move_listbox.insert(tk.END, display_text)
    
    def on_move_select(self, event):
        """
        Handle move selection
        """
        selection = self.move_listbox.curselection()
        if selection:
            # Extract move ID from selection
            selected_text = self.move_listbox.get(selection[0])
            move_id = int(selected_text.split(' - ')[0])
            self.current_move_id = move_id
            
            # Update preview
            self.update_preview()
    
    def update_preview(self):
        """
        Update the preview area with current move info
        """
        if self.current_move_id and self.move_extractor:
            moves = self.move_extractor.get_move_list()
            current_move = next((m for m in moves if m['id'] == self.current_move_id), None)
            
            if current_move:
                self.log_message(f"Selected move: {current_move['name']} (ID: {current_move['id']})")
    
    def extract_current_move(self):
        """
        Extract the currently selected move
        """
        if not self.current_move_id or not self.move_extractor:
            messagebox.showwarning("Warning", "Please select a move first")
            return
        
        self.status_var.set("Extracting move...")
        self.log_message(f"Extracting move ID {self.current_move_id}...")
        
        # Extract in separate thread
        threading.Thread(target=self._extract_move_thread, daemon=True).start()
    
    def _extract_move_thread(self):
        """
        Extract move in a separate thread
        """
        try:
            # Extract move graphics
            extracted_data = self.move_extractor.extract_move_graphics(self.current_move_id)
            
            # Save extracted data
            self.move_extractor.save_move_data(self.current_move_id, extracted_data)
            
            # Update preview with extracted images
            self.extracted_images = extracted_data
            self.root.after(0, self._update_preview_after_extract)
            
        except Exception as e:
            error_msg = f"Error extracting move: {str(e)}"
            self.root.after(0, lambda: self.log_message(error_msg))
    
    def _update_preview_after_extract(self):
        """
        Update preview after extraction
        """
        # Collect all images for preview
        all_images = []
        for category, images in self.extracted_images.items():
            for i, img in enumerate(images):
                all_images.append((f"{category}_{i}", img))
        
        self.current_images = all_images
        self.current_image_index = 0
        
        if all_images:
            self.display_current_image()
            self.prev_button.config(state="normal")
            self.next_button.config(state="normal")
        
        self.status_var.set("Extraction complete")
        self.log_message(f"Extraction complete. Found {len(all_images)} images.")
    
    def display_current_image(self):
        """
        Display the current image in the preview
        """
        if not self.current_images:
            return
        
        name, img = self.current_images[self.current_image_index]
        
        # Resize image to fit canvas
        canvas_width = self.image_canvas.winfo_width()
        canvas_height = self.image_canvas.winfo_height()
        
        if canvas_width > 1 and canvas_height > 1:
            img_resized = img.copy()
            img_resized.thumbnail((canvas_width - 20, canvas_height - 20), Image.Resampling.LANCZOS)
            
            # Convert to PhotoImage
            self.current_photo = ImageTk.PhotoImage(img_resized)
            
            # Clear canvas and display image
            self.image_canvas.delete("all")
            x = (canvas_width - img_resized.width) // 2
            y = (canvas_height - img_resized.height) // 2
            self.image_canvas.create_image(x, y, anchor=tk.NW, image=self.current_photo)
            
            # Update info label
            info_text = f"{name} ({self.current_image_index + 1}/{len(self.current_images)})"
            self.image_info_label.config(text=info_text)
    
    def prev_image(self):
        """
        Show previous image
        """
        if self.current_images and self.current_image_index > 0:
            self.current_image_index -= 1
            self.display_current_image()
    
    def next_image(self):
        """
        Show next image
        """
        if self.current_images and self.current_image_index < len(self.current_images) - 1:
            self.current_image_index += 1
            self.display_current_image()


def main():
    """
    Main function to run the application
    """
    root = tk.Tk()
    app = PokemonMoveExtractorGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
